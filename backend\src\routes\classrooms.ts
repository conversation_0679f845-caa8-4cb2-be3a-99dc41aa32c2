import { Router } from 'express';
import { ClassroomController } from '@/controllers/classroomController';
import { validate, createClassroomSchema, updateClassroomSchema } from '@/utils/validation';

const router = Router();
const classroomController = new ClassroomController();

// Classroom CRUD routes
router.post('/', validate(createClassroomSchema), classroomController.createClassroom);
router.get('/', classroomController.getClassrooms);
router.get('/:id', classroomController.getClassroomById);
router.patch('/:id', validate(updateClassroomSchema), classroomController.updateClassroom);
router.delete('/:id', classroomController.deleteClassroom);

export default router;
