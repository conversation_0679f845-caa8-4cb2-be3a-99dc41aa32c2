import { Request } from 'express';
import { Document, Types } from 'mongoose';

// User Types
export interface IUser extends Document {
  _id: Types.ObjectId;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  isEmailVerified: boolean;
  refreshTokens: string[];
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateTokens(): Promise<{ accessToken: string; refreshToken: string }>;
}

export interface IUserInput {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

export interface IUserResponse {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  isEmailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Task Types
export interface ITask extends Document {
  _id: Types.ObjectId;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  category?: string;
  color?: string;
  isRecurring: boolean;
  recurringPattern?: {
    type: 'daily' | 'weekly' | 'monthly';
    interval: number;
    daysOfWeek?: number[]; // 0-6 (Sunday-Saturday)
    endDate?: Date;
  };
  userId: Types.ObjectId;
  scheduleId: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITaskInput {
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  priority?: 'low' | 'medium' | 'high';
  status?: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  category?: string;
  color?: string;
  isRecurring?: boolean;
  recurringPattern?: {
    type: 'daily' | 'weekly' | 'monthly';
    interval: number;
    daysOfWeek?: number[];
    endDate?: Date;
  };
  scheduleId: string;
}

// Schedule Types
export interface ISchedule extends Document {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  isDefault: boolean;
  userId: Types.ObjectId;
  tasks: Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IScheduleInput {
  name: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  isDefault?: boolean;
}

// Teacher Types
export interface ITeacher extends Document {
  _id: Types.ObjectId;
  name: string;
  color: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITeacherInput {
  name: string;
  color: string;
}

// Classroom Types
export interface IClassroom extends Document {
  _id: Types.ObjectId;
  name: string;
  capacity: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IClassroomInput {
  name: string;
  capacity: number;
}

// Level Types
export interface ILevel extends Document {
  _id: Types.ObjectId;
  label: string;
  color: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ILevelInput {
  label: string;
  color: string;
}

// TimeSlot Types
export interface ITimeSlot extends Document {
  _id: Types.ObjectId;
  teacherId: Types.ObjectId;
  classroomId: Types.ObjectId;
  levelId: Types.ObjectId;
  start: string;
  end: string;
  dayOfWeek: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITimeSlotInput {
  teacherId: string;
  classroomId: string;
  levelId: string;
  start: string;
  end: string;
  dayOfWeek: number;
}

// Authentication Types
export interface IAuthRequest extends Request {
  user?: IUser;
}

export interface ILoginInput {
  email: string;
  password: string;
}

export interface IRegisterInput {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface ITokenPayload {
  userId: string;
  email: string;
  iat: number;
  exp: number;
}

export interface IRefreshTokenPayload {
  userId: string;
  tokenId: string;
  iat: number;
  exp: number;
}

// API Response Types
export interface IApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: Record<string, string>;
}

export interface IPaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface IPaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// Error Types
export interface ICustomError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

// Query Types
export interface ITaskQuery extends IPaginationQuery {
  status?: string;
  priority?: string;
  category?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface IScheduleQuery extends IPaginationQuery {
  search?: string;
  isDefault?: boolean;
}
