import { toast } from 'sonner';
import { ApiError } from './utils';

// Notification types with gentle styling
export const notify = {
  success: (message: string, description?: string) => {
    toast.success(message, {
      description,
      duration: 4000,
    });
  },

  info: (message: string, description?: string) => {
    toast.info(message, {
      description,
      duration: 4000,
    });
  },

  warning: (message: string, description?: string) => {
    toast.warning(message, {
      description,
      duration: 5000,
    });
  },

  // Gentle error notification - not intimidating red
  error: (message: string, description?: string) => {
    toast.error(message, {
      description,
      duration: 6000,
      style: {
        backgroundColor: '#fef3f2',
        borderColor: '#fecaca',
        color: '#7f1d1d',
      },
    });
  },

  // Handle API errors with user-friendly messages
  apiError: (error: unknown) => {
    if (error instanceof ApiError) {
      const message = getErrorMessage(error);
      const description = error.errors 
        ? Object.values(error.errors).join(', ')
        : undefined;
      
      notify.warning(message, description);
    } else if (error instanceof Error) {
      notify.warning('Something went wrong', error.message);
    } else {
      notify.warning('An unexpected error occurred', 'Please try again');
    }
  },

  // Loading states
  loading: (message: string) => {
    return toast.loading(message);
  },

  // Dismiss a specific toast
  dismiss: (toastId: string | number) => {
    toast.dismiss(toastId);
  },
};

// Convert technical error messages to user-friendly ones
function getErrorMessage(error: ApiError): string {
  const { message, statusCode } = error;

  // Network errors
  if (statusCode === 0) {
    return 'Connection issue';
  }

  // Server errors
  if (statusCode >= 500) {
    return 'Server is temporarily unavailable';
  }

  // Client errors
  if (statusCode === 404) {
    return 'Requested item not found';
  }

  if (statusCode === 401) {
    return 'Please log in to continue';
  }

  if (statusCode === 403) {
    return 'You don\'t have permission for this action';
  }

  if (statusCode === 409) {
    return message || 'This item already exists';
  }

  if (statusCode === 422 || statusCode === 400) {
    return message || 'Please check your input';
  }

  // Default to the original message if it's user-friendly
  if (message && !message.includes('Error:') && !message.includes('Exception')) {
    return message;
  }

  return 'Something didn\'t work as expected';
}

// Async operation wrapper with notifications
export async function withNotifications<T>(
  operation: () => Promise<T>,
  options: {
    loading?: string;
    success?: string;
    errorPrefix?: string;
  } = {}
): Promise<T | null> {
  const {
    loading = 'Processing...',
    success,
    errorPrefix = ''
  } = options;

  const loadingToast = notify.loading(loading);

  try {
    const result = await operation();
    
    notify.dismiss(loadingToast);
    
    if (success) {
      notify.success(success);
    }
    
    return result;
  } catch (error) {
    notify.dismiss(loadingToast);
    
    if (error instanceof ApiError && errorPrefix) {
      const message = `${errorPrefix}: ${getErrorMessage(error)}`;
      notify.warning(message);
    } else {
      notify.apiError(error);
    }
    
    return null;
  }
}
