import { Router } from 'express';
import { TaskController } from '@/controllers/taskController';
import { authenticate } from '@/middleware/auth';
import { validate, createTaskSchema, updateTaskSchema } from '@/utils/validation';

const router = Router();
const taskController = new TaskController();

// All task routes require authentication
router.use(authenticate);

// Task CRUD routes
router.post('/', validate(createTaskSchema), taskController.createTask);
router.get('/', taskController.getTasks);
router.get('/today', taskController.getTodayTasks);
router.get('/overdue', taskController.getOverdueTasks);
router.get('/date-range', taskController.getTasksByDateRange);
router.get('/:id', taskController.getTaskById);
router.patch('/:id', validate(updateTaskSchema), taskController.updateTask);
router.delete('/:id', taskController.deleteTask);

export default router;
