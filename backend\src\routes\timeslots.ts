import { Router } from 'express';
import { TimeSlotController } from '@/controllers/timeSlotController';
import { validate, createTimeSlotSchema, updateTimeSlotSchema } from '@/utils/validation';

const router = Router();
const timeSlotController = new TimeSlotController();

// Time slot CRUD routes
router.post('/', validate(createTimeSlotSchema), timeSlotController.createTimeSlot);
router.get('/', timeSlotController.getTimeSlots);
router.get('/:id', timeSlotController.getTimeSlotById);
router.patch('/:id', validate(updateTimeSlotSchema), timeSlotController.updateTimeSlot);
router.delete('/:id', timeSlotController.deleteTimeSlot);

// Note: Bulk endpoint for schedule data is in a separate route file

export default router;
