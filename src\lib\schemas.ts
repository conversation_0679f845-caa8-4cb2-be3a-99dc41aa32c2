
import { z } from 'zod';

export const TeacherSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
});

export const ClassroomSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
});

export const LevelSchema = z.object({
  id: z.string(),
  label: z.string().min(1, 'Label is required'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
});

export const TimeSlotSchema = z.object({
  id: z.string(),
  teacherId: z.string(),
  classroomId: z.string(),
  levelId: z.string(),
  start: z.string(),
  end: z.string(),
  dayOfWeek: z.number().min(0).max(6),
});

export type Teacher = z.infer<typeof TeacherSchema>;
export type Classroom = z.infer<typeof ClassroomSchema>;
export type Level = z.infer<typeof LevelSchema>;
export type TimeSlot = z.infer<typeof TimeSlotSchema>;
