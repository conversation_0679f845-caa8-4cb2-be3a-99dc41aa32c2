import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { API_BASE_URL } from '@/lib/utils';

export const ConnectionStatus: React.FC = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkConnection = async () => {
    setIsChecking(true);
    try {
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      setIsConnected(response.ok);
    } catch (error) {
      setIsConnected(false);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnection();
    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isChecking && isConnected === null) {
    return (
      <Badge variant="secondary" className="text-xs">
        Checking connection...
      </Badge>
    );
  }

  return (
    <Badge 
      variant={isConnected ? "default" : "destructive"} 
      className="text-xs cursor-pointer"
      onClick={checkConnection}
      title="Click to refresh connection status"
    >
      {isConnected ? '🟢 Connected' : '🔴 Offline'}
    </Badge>
  );
};
