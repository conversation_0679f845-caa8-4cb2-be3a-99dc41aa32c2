import { Router } from 'express';
import { LevelController } from '@/controllers/levelController';
import { validate, createLevelSchema, updateLevelSchema } from '@/utils/validation';

const router = Router();
const levelController = new LevelController();

// Level CRUD routes
router.post('/', validate(createLevelSchema), levelController.createLevel);
router.get('/', levelController.getLevels);
router.get('/:id', levelController.getLevelById);
router.patch('/:id', validate(updateLevelSchema), levelController.updateLevel);
router.delete('/:id', levelController.deleteLevel);

export default router;
