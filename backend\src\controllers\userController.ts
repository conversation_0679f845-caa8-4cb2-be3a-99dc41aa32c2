import { Request, Response, NextFunction } from 'express';
import { User } from '@/models/User';
import { AppError } from '@/utils/AppError';
import { IAuthRequest } from '@/types';

export class UserController {
  // Update user profile
  public async updateProfile(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { firstName, lastName, avatar } = req.body;

      // Update user fields
      if (firstName) user.firstName = firstName;
      if (lastName) user.lastName = lastName;
      if (avatar !== undefined) user.avatar = avatar;

      await user.save();

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: {
          user: user.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Change password
  public async changePassword(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { currentPassword, newPassword } = req.body;

      // Get user with password
      const userWithPassword = await User.findById(user._id).select('+password');
      if (!userWithPassword) {
        throw new AppError('User not found', 404);
      }

      // Verify current password
      const isCurrentPasswordValid = await userWithPassword.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new AppError('Current password is incorrect', 400);
      }

      // Update password
      userWithPassword.password = newPassword;
      await userWithPassword.save();

      res.json({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete user account
  public async deleteAccount(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { password } = req.body;

      // Get user with password
      const userWithPassword = await User.findById(user._id).select('+password');
      if (!userWithPassword) {
        throw new AppError('User not found', 404);
      }

      // Verify password
      const isPasswordValid = await userWithPassword.comparePassword(password);
      if (!isPasswordValid) {
        throw new AppError('Password is incorrect', 400);
      }

      // Delete user
      await User.findByIdAndDelete(user._id);

      // Clear refresh token cookie
      res.clearCookie('refreshToken');

      res.json({
        success: true,
        message: 'Account deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
