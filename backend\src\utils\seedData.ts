import { Teacher } from '@/models/Teacher';
import { Classroom } from '@/models/Classroom';
import { Level } from '@/models/Level';

export async function seedInitialData() {
  try {
    console.log('🌱 Checking and seeding initial data...');

    // Check and seed teachers
    const teacherCount = await Teacher.countDocuments();
    if (teacherCount === 0) {
      const teachers = await Teacher.insertMany([
        { name: 'Mr<PERSON>', color: '#3B82F6' },
        { name: '<PERSON><PERSON>', color: '#EF4444' },
        { name: 'Dr<PERSON>', color: '#10B981' },
        { name: 'Prof<PERSON>', color: '#F59E0B' },
        { name: 'Mrs. <PERSON>', color: '#8B5CF6' },
      ]);
      console.log(`✅ Seeded ${teachers.length} teachers`);
    } else {
      console.log(`📚 Found ${teacherCount} existing teachers`);
    }

    // Check and seed classrooms
    const classroomCount = await Classroom.countDocuments();
    if (classroomCount === 0) {
      const classrooms = await Classroom.insertMany([
        { name: 'Room 101', capacity: 30 },
        { name: 'Room 102', capacity: 25 },
        { name: 'Lab 1', capacity: 20 },
        { name: 'Lab 2', capacity: 20 },
        { name: 'Auditorium', capacity: 100 },
        { name: 'Conference Room', capacity: 15 },
      ]);
      console.log(`✅ Seeded ${classrooms.length} classrooms`);
    } else {
      console.log(`📚 Found ${classroomCount} existing classrooms`);
    }

    // Check and seed levels
    const levelCount = await Level.countDocuments();
    if (levelCount === 0) {
      const levels = await Level.insertMany([
        { label: 'Beginner', color: '#22C55E' },
        { label: 'Intermediate', color: '#F59E0B' },
        { label: 'Advanced', color: '#EF4444' },
        { label: 'Expert', color: '#8B5CF6' },
      ]);
      console.log(`✅ Seeded ${levels.length} levels`);
    } else {
      console.log(`📚 Found ${levelCount} existing levels`);
    }

    console.log('🎉 Initial data check complete');
  } catch (error) {
    console.error('❌ Error seeding initial data:', error);
    throw error;
  }
}
