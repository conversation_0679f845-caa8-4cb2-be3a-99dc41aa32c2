import mongoose, { Schema } from 'mongoose';
import { ISchedule } from '@/types';

const scheduleSchema = new Schema<ISchedule>(
  {
    name: {
      type: String,
      required: [true, 'Schedule name is required'],
      trim: true,
      maxlength: [100, 'Schedule name cannot exceed 100 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
    },
    endDate: {
      type: Date,
      required: [true, 'End date is required'],
      validate: {
        validator: function(this: ISchedule, value: Date) {
          return value > this.startDate;
        },
        message: 'End date must be after start date',
      },
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    tasks: [{
      type: Schema.Types.ObjectId,
      ref: 'Task',
    }],
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for better query performance
scheduleSchema.index({ userId: 1, createdAt: -1 });
scheduleSchema.index({ userId: 1, isDefault: 1 });
scheduleSchema.index({ startDate: 1, endDate: 1 });
scheduleSchema.index({ name: 'text', description: 'text' });

// Ensure only one default schedule per user
scheduleSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    // Remove default flag from other schedules of the same user
    await mongoose.model('Schedule').updateMany(
      { userId: this.userId, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

// Virtual to get schedule duration in days
scheduleSchema.virtual('durationDays').get(function() {
  const diffTime = Math.abs(this.endDate.getTime() - this.startDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual to check if schedule is active
scheduleSchema.virtual('isActive').get(function() {
  const now = new Date();
  return now >= this.startDate && now <= this.endDate;
});

// Virtual to get task count
scheduleSchema.virtual('taskCount').get(function() {
  return this.tasks.length;
});

// Ensure virtual fields are serialized
scheduleSchema.set('toJSON', { virtuals: true });

// Static method to find user's default schedule
scheduleSchema.statics.findDefaultByUser = function(userId: string) {
  return this.findOne({ userId, isDefault: true });
};

// Static method to find active schedules
scheduleSchema.statics.findActive = function(userId?: string) {
  const now = new Date();
  const query: any = {
    startDate: { $lte: now },
    endDate: { $gte: now },
  };
  
  if (userId) {
    query.userId = userId;
  }
  
  return this.find(query);
};

export const Schedule = mongoose.model<ISchedule>('Schedule', scheduleSchema);
