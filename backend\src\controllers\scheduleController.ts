import { Request, Response, NextFunction } from 'express';
import { Schedule } from '@/models/Schedule';
import { Task } from '@/models/Task';
import { AppError } from '@/utils/AppError';
import { IAuthRequest, IScheduleInput, IScheduleQuery } from '@/types';

export class ScheduleController {
  // Create new schedule
  public async createSchedule(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const scheduleData: IScheduleInput = req.body;

      const schedule = new Schedule({
        ...scheduleData,
        userId: user._id,
      });

      await schedule.save();

      res.status(201).json({
        success: true,
        message: 'Schedule created successfully',
        data: {
          schedule: schedule.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all user schedules
  public async getSchedules(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { page = 1, limit = 10, search, isDefault }: IScheduleQuery = req.query;

      // Build query
      const query: any = { userId: user._id };
      
      if (search) {
        query.$text = { $search: search };
      }
      
      if (isDefault !== undefined) {
        query.isDefault = isDefault === 'true';
      }

      // Calculate pagination
      const skip = (Number(page) - 1) * Number(limit);

      // Get schedules with pagination
      const [schedules, totalCount] = await Promise.all([
        Schedule.find(query)
          .sort({ isDefault: -1, createdAt: -1 })
          .skip(skip)
          .limit(Number(limit))
          .populate('tasks', 'title startTime endTime status priority'),
        Schedule.countDocuments(query),
      ]);

      const totalPages = Math.ceil(totalCount / Number(limit));

      res.json({
        success: true,
        message: 'Schedules retrieved successfully',
        data: {
          schedules,
          pagination: {
            currentPage: Number(page),
            totalPages,
            totalItems: totalCount,
            itemsPerPage: Number(limit),
            hasNextPage: Number(page) < totalPages,
            hasPrevPage: Number(page) > 1,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get schedule by ID
  public async getScheduleById(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;

      const schedule = await Schedule.findOne({ _id: id, userId: user._id })
        .populate('tasks', 'title description startTime endTime status priority category color');

      if (!schedule) {
        throw new AppError('Schedule not found', 404);
      }

      res.json({
        success: true,
        message: 'Schedule retrieved successfully',
        data: {
          schedule: schedule.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update schedule
  public async updateSchedule(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;
      const updateData = req.body;

      const schedule = await Schedule.findOneAndUpdate(
        { _id: id, userId: user._id },
        updateData,
        { new: true, runValidators: true }
      );

      if (!schedule) {
        throw new AppError('Schedule not found', 404);
      }

      res.json({
        success: true,
        message: 'Schedule updated successfully',
        data: {
          schedule: schedule.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete schedule
  public async deleteSchedule(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;

      const schedule = await Schedule.findOne({ _id: id, userId: user._id });

      if (!schedule) {
        throw new AppError('Schedule not found', 404);
      }

      // Don't allow deletion of default schedule if it's the only one
      if (schedule.isDefault) {
        const scheduleCount = await Schedule.countDocuments({ userId: user._id });
        if (scheduleCount === 1) {
          throw new AppError('Cannot delete the only schedule', 400);
        }
      }

      // Delete all tasks associated with this schedule
      await Task.deleteMany({ scheduleId: id });

      // Delete the schedule
      await Schedule.findByIdAndDelete(id);

      res.json({
        success: true,
        message: 'Schedule deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get default schedule
  public async getDefaultSchedule(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;

      let schedule = await Schedule.findOne({ userId: user._id, isDefault: true })
        .populate('tasks', 'title description startTime endTime status priority category color');

      // If no default schedule exists, create one
      if (!schedule) {
        const now = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + 7); // Default to 1 week

        schedule = new Schedule({
          name: 'My Schedule',
          description: 'Default weekly schedule',
          startDate: now,
          endDate,
          isDefault: true,
          userId: user._id,
        });

        await schedule.save();
      }

      res.json({
        success: true,
        message: 'Default schedule retrieved successfully',
        data: {
          schedule: schedule.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
