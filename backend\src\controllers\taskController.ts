import { Request, Response, NextFunction } from 'express';
import { Task } from '@/models/Task';
import { Schedule } from '@/models/Schedule';
import { AppError } from '@/utils/AppError';
import { IAuthRequest, ITaskInput, ITaskQuery } from '@/types';

export class TaskController {
  // Create new task
  public async createTask(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const taskData: ITaskInput = req.body;

      // Verify schedule belongs to user
      const schedule = await Schedule.findOne({ _id: taskData.scheduleId, userId: user._id });
      if (!schedule) {
        throw new AppError('Schedule not found', 404);
      }

      const task = new Task({
        ...taskData,
        userId: user._id,
      });

      await task.save();

      // Add task to schedule
      schedule.tasks.push(task._id);
      await schedule.save();

      res.status(201).json({
        success: true,
        message: 'Task created successfully',
        data: {
          task: task.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all user tasks
  public async getTasks(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const {
        page = 1,
        limit = 10,
        status,
        priority,
        category,
        startDate,
        endDate,
        search,
        sortBy = 'startTime',
        sortOrder = 'asc'
      }: ITaskQuery = req.query;

      // Build query
      const query: any = { userId: user._id };

      if (status) query.status = status;
      if (priority) query.priority = priority;
      if (category) query.category = category;

      if (startDate || endDate) {
        query.startTime = {};
        if (startDate) query.startTime.$gte = new Date(startDate);
        if (endDate) query.startTime.$lte = new Date(endDate);
      }

      if (search) {
        query.$text = { $search: search };
      }

      // Calculate pagination
      const skip = (Number(page) - 1) * Number(limit);

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // Get tasks with pagination
      const [tasks, totalCount] = await Promise.all([
        Task.find(query)
          .sort(sort)
          .skip(skip)
          .limit(Number(limit))
          .populate('scheduleId', 'name'),
        Task.countDocuments(query),
      ]);

      const totalPages = Math.ceil(totalCount / Number(limit));

      res.json({
        success: true,
        message: 'Tasks retrieved successfully',
        data: {
          tasks,
          pagination: {
            currentPage: Number(page),
            totalPages,
            totalItems: totalCount,
            itemsPerPage: Number(limit),
            hasNextPage: Number(page) < totalPages,
            hasPrevPage: Number(page) > 1,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get task by ID
  public async getTaskById(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;

      const task = await Task.findOne({ _id: id, userId: user._id })
        .populate('scheduleId', 'name description');

      if (!task) {
        throw new AppError('Task not found', 404);
      }

      res.json({
        success: true,
        message: 'Task retrieved successfully',
        data: {
          task: task.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update task
  public async updateTask(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;
      const updateData = req.body;

      const task = await Task.findOneAndUpdate(
        { _id: id, userId: user._id },
        updateData,
        { new: true, runValidators: true }
      );

      if (!task) {
        throw new AppError('Task not found', 404);
      }

      res.json({
        success: true,
        message: 'Task updated successfully',
        data: {
          task: task.toJSON(),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete task
  public async deleteTask(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;

      const task = await Task.findOneAndDelete({ _id: id, userId: user._id });

      if (!task) {
        throw new AppError('Task not found', 404);
      }

      // Remove task from schedule
      await Schedule.updateOne(
        { _id: task.scheduleId },
        { $pull: { tasks: task._id } }
      );

      res.json({
        success: true,
        message: 'Task deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get today's tasks
  public async getTodayTasks(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;

      const tasks = await (Task as any).findTodayTasks(user._id.toString());

      res.json({
        success: true,
        message: "Today's tasks retrieved successfully",
        data: {
          tasks,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get overdue tasks
  public async getOverdueTasks(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;

      const tasks = await (Task as any).findOverdue(user._id.toString());

      res.json({
        success: true,
        message: 'Overdue tasks retrieved successfully',
        data: {
          tasks,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get tasks by date range
  public async getTasksByDateRange(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { startDate, endDate } = req.query;

      if (!startDate || !endDate) {
        throw new AppError('Start date and end date are required', 400);
      }

      const tasks = await (Task as any).findByDateRange(
        user._id.toString(),
        new Date(startDate as string),
        new Date(endDate as string)
      );

      res.json({
        success: true,
        message: 'Tasks retrieved successfully',
        data: {
          tasks,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
