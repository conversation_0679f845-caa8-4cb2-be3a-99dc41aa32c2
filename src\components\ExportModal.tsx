
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from '@/hooks/use-toast';
import { Download, FileText, Sheet } from 'lucide-react';
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ExportModal: React.FC<ExportModalProps> = ({ isOpen, onClose }) => {
  const { timeSlots, teachers, classrooms, levels } = useStore();
  
  const [exportType, setExportType] = useState<'whole' | 'teacher' | 'classroom'>('whole');
  const [format, setFormat] = useState<'pdf' | 'excel'>('pdf');
  const [selectedId, setSelectedId] = useState('');

  const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  const getFilteredTimeSlots = () => {
    if (exportType === 'whole') return timeSlots;
    if (exportType === 'teacher') return timeSlots.filter(slot => slot.teacherId === selectedId);
    if (exportType === 'classroom') return timeSlots.filter(slot => slot.classroomId === selectedId);
    return [];
  };

  const generatePDF = () => {
    const pdf = new jsPDF('landscape');
    const filteredSlots = getFilteredTimeSlots();
    
    // Title
    let title = 'Weekly Timetable';
    if (exportType === 'teacher') {
      const teacher = teachers.find(t => t.id === selectedId);
      title += ` - ${teacher?.name}`;
    } else if (exportType === 'classroom') {
      const classroom = classrooms.find(c => c.id === selectedId);
      title += ` - ${classroom?.name}`;
    }
    
    pdf.setFontSize(20);
    pdf.text(title, 20, 20);

    // Create a simple table
    let yPosition = 40;
    pdf.setFontSize(12);
    
    DAYS.forEach((day, dayIndex) => {
      const daySlots = filteredSlots.filter(slot => slot.dayOfWeek === dayIndex);
      
      if (daySlots.length > 0) {
        pdf.setFontSize(14);
        pdf.text(day, 20, yPosition);
        yPosition += 10;
        
        daySlots.forEach(slot => {
          const teacher = teachers.find(t => t.id === slot.teacherId);
          const classroom = classrooms.find(c => c.id === slot.classroomId);
          const level = levels.find(l => l.id === slot.levelId);
          
          const text = `${slot.start} - ${slot.end}: ${teacher?.name} | ${classroom?.name} | ${level?.label}`;
          pdf.setFontSize(10);
          pdf.text(text, 30, yPosition);
          yPosition += 8;
        });
        
        yPosition += 5;
      }
    });

    pdf.save(`timetable-${exportType}-${Date.now()}.pdf`);
  };

  const generateExcel = () => {
    const filteredSlots = getFilteredTimeSlots();
    
    const data = filteredSlots.map(slot => {
      const teacher = teachers.find(t => t.id === slot.teacherId);
      const classroom = classrooms.find(c => c.id === slot.classroomId);
      const level = levels.find(l => l.id === slot.levelId);
      
      return {
        Day: DAYS[slot.dayOfWeek],
        'Start Time': slot.start,
        'End Time': slot.end,
        Teacher: teacher?.name,
        Classroom: classroom?.name,
        Level: level?.label,
      };
    });

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Timetable');
    
    XLSX.writeFile(wb, `timetable-${exportType}-${Date.now()}.xlsx`);
  };

  const handleExport = () => {
    if (exportType !== 'whole' && !selectedId) {
      toast({
        title: "Selection Required",
        description: `Please select a ${exportType} to export.`,
        variant: "destructive",
      });
      return;
    }

    try {
      if (format === 'pdf') {
        generatePDF();
      } else {
        generateExcel();
      }
      
      toast({
        title: "Export Successful",
        description: `Timetable exported as ${format.toUpperCase()}.`,
      });
      
      onClose();
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "An error occurred while exporting the timetable.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-card border-white/20">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold flex items-center">
            <Download className="h-5 w-5 mr-2" />
            Export Timetable
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Export Type */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Type</Label>
            <RadioGroup value={exportType} onValueChange={(value: any) => setExportType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="whole" id="whole" />
                <Label htmlFor="whole">Whole Week</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="teacher" id="teacher" />
                <Label htmlFor="teacher">Teacher Specific</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="classroom" id="classroom" />
                <Label htmlFor="classroom">Classroom Specific</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Selection */}
          {exportType !== 'whole' && (
            <div className="space-y-2">
              <Label>Select {exportType === 'teacher' ? 'Teacher' : 'Classroom'}</Label>
              <Select value={selectedId} onValueChange={setSelectedId}>
                <SelectTrigger className="glass-button">
                  <SelectValue placeholder={`Select a ${exportType}`} />
                </SelectTrigger>
                <SelectContent>
                  {(exportType === 'teacher' ? teachers : classrooms).map((item) => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Format</Label>
            <RadioGroup value={format} onValueChange={(value: any) => setFormat(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="pdf" id="pdf" />
                <Label htmlFor="pdf" className="flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  PDF
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="excel" id="excel" />
                <Label htmlFor="excel" className="flex items-center">
                  <Sheet className="h-4 w-4 mr-2" />
                  Excel
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose} className="glass-button">
              Cancel
            </Button>
            <Button onClick={handleExport} className="glass-button bg-primary/20 hover:bg-primary/30">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};
