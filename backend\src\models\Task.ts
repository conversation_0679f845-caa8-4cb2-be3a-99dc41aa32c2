import mongoose, { Schema } from 'mongoose';
import { ITask } from '@/types';

const recurringPatternSchema = new Schema({
  type: {
    type: String,
    enum: ['daily', 'weekly', 'monthly'],
    required: true,
  },
  interval: {
    type: Number,
    required: true,
    min: [1, 'Interval must be at least 1'],
  },
  daysOfWeek: [{
    type: Number,
    min: 0,
    max: 6,
  }],
  endDate: {
    type: Date,
  },
}, { _id: false });

const taskSchema = new Schema<ITask>(
  {
    title: {
      type: String,
      required: [true, 'Task title is required'],
      trim: true,
      maxlength: [200, 'Title cannot exceed 200 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [1000, 'Description cannot exceed 1000 characters'],
    },
    startTime: {
      type: Date,
      required: [true, 'Start time is required'],
    },
    endTime: {
      type: Date,
      required: [true, 'End time is required'],
      validate: {
        validator: function(this: ITask, value: Date) {
          return value > this.startTime;
        },
        message: 'End time must be after start time',
      },
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium',
    },
    status: {
      type: String,
      enum: ['pending', 'in-progress', 'completed', 'cancelled'],
      default: 'pending',
    },
    category: {
      type: String,
      trim: true,
      maxlength: [50, 'Category cannot exceed 50 characters'],
    },
    color: {
      type: String,
      match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please provide a valid hex color'],
      default: '#3B82F6',
    },
    isRecurring: {
      type: Boolean,
      default: false,
    },
    recurringPattern: {
      type: recurringPatternSchema,
      required: function(this: ITask) {
        return this.isRecurring;
      },
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    scheduleId: {
      type: Schema.Types.ObjectId,
      ref: 'Schedule',
      required: [true, 'Schedule ID is required'],
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for better query performance
taskSchema.index({ userId: 1, scheduleId: 1 });
taskSchema.index({ startTime: 1, endTime: 1 });
taskSchema.index({ status: 1, priority: 1 });
taskSchema.index({ category: 1 });
taskSchema.index({ title: 'text', description: 'text' });
taskSchema.index({ userId: 1, startTime: 1 });

// Virtual to get task duration in minutes
taskSchema.virtual('durationMinutes').get(function() {
  const diffTime = this.endTime.getTime() - this.startTime.getTime();
  return Math.round(diffTime / (1000 * 60));
});

// Virtual to check if task is overdue
taskSchema.virtual('isOverdue').get(function() {
  const now = new Date();
  return this.status !== 'completed' && this.status !== 'cancelled' && this.endTime < now;
});

// Virtual to check if task is today
taskSchema.virtual('isToday').get(function() {
  const today = new Date();
  const taskDate = new Date(this.startTime);
  return (
    taskDate.getDate() === today.getDate() &&
    taskDate.getMonth() === today.getMonth() &&
    taskDate.getFullYear() === today.getFullYear()
  );
});

// Ensure virtual fields are serialized
taskSchema.set('toJSON', { virtuals: true });

// Static method to find tasks by date range
taskSchema.statics.findByDateRange = function(userId: string, startDate: Date, endDate: Date) {
  return this.find({
    userId,
    $or: [
      {
        startTime: { $gte: startDate, $lte: endDate }
      },
      {
        endTime: { $gte: startDate, $lte: endDate }
      },
      {
        startTime: { $lte: startDate },
        endTime: { $gte: endDate }
      }
    ]
  }).sort({ startTime: 1 });
};

// Static method to find today's tasks
taskSchema.statics.findTodayTasks = function(userId: string) {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
  
  return this.findByDateRange(userId, startOfDay, endOfDay);
};

// Static method to find overdue tasks
taskSchema.statics.findOverdue = function(userId: string) {
  const now = new Date();
  return this.find({
    userId,
    endTime: { $lt: now },
    status: { $nin: ['completed', 'cancelled'] }
  }).sort({ endTime: 1 });
};

export const Task = mongoose.model<ITask>('Task', taskSchema);
