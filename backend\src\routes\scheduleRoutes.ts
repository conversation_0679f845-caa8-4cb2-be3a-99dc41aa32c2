import { Router } from 'express';
import { Request, Response } from 'express';

const router = Router();

// Mock data for development
const mockTimeSlots = [
  {
    id: 1,
    day: 'Monday',
    startTime: '09:00',
    endTime: '10:00',
    subject: 'Mathematics',
    teacher: 'Mr<PERSON> <PERSON>',
    classroom: 'Room 101'
  },
  {
    id: 2,
    day: 'Monday',
    startTime: '10:00',
    endTime: '11:00',
    subject: 'English',
    teacher: '<PERSON><PERSON>',
    classroom: 'Room 102'
  },
  {
    id: 3,
    day: 'Tuesday',
    startTime: '09:00',
    endTime: '10:00',
    subject: 'Science',
    teacher: 'Dr<PERSON>',
    classroom: 'Lab 1'
  }
];

// GET /api/schedules/timeslots - Get all time slots
router.get('/timeslots', (req: Request, res: Response) => {
  try {
    res.json({
      success: true,
      data: mockTimeSlots,
      message: 'Time slots retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve time slots',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/schedules/timeslots - Create a new time slot
router.post('/timeslots', (req: Request, res: Response) => {
  try {
    const { day, startTime, endTime, subject, teacher, classroom } = req.body;
    
    // Basic validation
    if (!day || !startTime || !endTime || !subject) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: day, startTime, endTime, subject'
      });
    }

    const newTimeSlot = {
      id: mockTimeSlots.length + 1,
      day,
      startTime,
      endTime,
      subject,
      teacher: teacher || '',
      classroom: classroom || ''
    };

    mockTimeSlots.push(newTimeSlot);

    res.status(201).json({
      success: true,
      data: newTimeSlot,
      message: 'Time slot created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create time slot',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/schedules/timeslots/:id - Update a time slot
router.put('/timeslots/:id', (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const { day, startTime, endTime, subject, teacher, classroom } = req.body;
    
    const timeSlotIndex = mockTimeSlots.findIndex(slot => slot.id === id);
    
    if (timeSlotIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Time slot not found'
      });
    }

    // Update the time slot
    mockTimeSlots[timeSlotIndex] = {
      ...mockTimeSlots[timeSlotIndex],
      day: day || mockTimeSlots[timeSlotIndex].day,
      startTime: startTime || mockTimeSlots[timeSlotIndex].startTime,
      endTime: endTime || mockTimeSlots[timeSlotIndex].endTime,
      subject: subject || mockTimeSlots[timeSlotIndex].subject,
      teacher: teacher || mockTimeSlots[timeSlotIndex].teacher,
      classroom: classroom || mockTimeSlots[timeSlotIndex].classroom
    };

    res.json({
      success: true,
      data: mockTimeSlots[timeSlotIndex],
      message: 'Time slot updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update time slot',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/schedules/timeslots/:id - Delete a time slot
router.delete('/timeslots/:id', (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const timeSlotIndex = mockTimeSlots.findIndex(slot => slot.id === id);
    
    if (timeSlotIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Time slot not found'
      });
    }

    const deletedTimeSlot = mockTimeSlots.splice(timeSlotIndex, 1)[0];

    res.json({
      success: true,
      data: deletedTimeSlot,
      message: 'Time slot deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete time slot',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/schedules/teachers - Get all teachers
router.get('/teachers', (req: Request, res: Response) => {
  try {
    const teachers = [...new Set(mockTimeSlots.map(slot => slot.teacher).filter(Boolean))];
    res.json({
      success: true,
      data: teachers,
      message: 'Teachers retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teachers',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/schedules/classrooms - Get all classrooms
router.get('/classrooms', (req: Request, res: Response) => {
  try {
    const classrooms = [...new Set(mockTimeSlots.map(slot => slot.classroom).filter(Boolean))];
    res.json({
      success: true,
      data: classrooms,
      message: 'Classrooms retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve classrooms',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/schedules/levels - Get all levels
router.get('/levels', (req: Request, res: Response) => {
  try {
    const levels = ['Beginner', 'Intermediate', 'Advanced'];
    res.json({
      success: true,
      data: levels,
      message: 'Levels retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve levels',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
