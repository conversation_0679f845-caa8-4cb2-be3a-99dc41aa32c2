import mongoose, { Schema, Document } from 'mongoose';

export interface ILevel extends Document {
  _id: mongoose.Types.ObjectId;
  label: string;
  color: string;
  createdAt: Date;
  updatedAt: Date;
}

const levelSchema = new Schema<ILevel>(
  {
    label: {
      type: String,
      required: [true, 'Level label is required'],
      trim: true,
      maxlength: [100, 'Label cannot exceed 100 characters'],
    },
    color: {
      type: String,
      required: [true, 'Color is required'],
      match: [/^#[0-9A-F]{6}$/i, 'Invalid color format'],
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for better query performance
levelSchema.index({ label: 1 });

export const Level = mongoose.model<ILevel>('Level', levelSchema);
