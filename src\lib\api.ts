import { apiRequest } from './utils';
import { Teacher, Classroom, Level, TimeSlot } from './schemas';

// API endpoints for schedule management
export const scheduleApi = {
  // Teachers
  async getTeachers(): Promise<Teacher[]> {
    return apiRequest<Teacher[]>('/teachers');
  },

  async createTeacher(teacher: Omit<Teacher, 'id'>): Promise<Teacher> {
    return apiRequest<Teacher>('/teachers', {
      method: 'POST',
      body: JSON.stringify(teacher),
    });
  },

  async updateTeacher(id: string, teacher: Partial<Teacher>): Promise<Teacher> {
    return apiRequest<Teacher>(`/teachers/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(teacher),
    });
  },

  async deleteTeacher(id: string): Promise<void> {
    return apiRequest<void>(`/teachers/${id}`, {
      method: 'DELETE',
    });
  },

  // Classrooms
  async getClassrooms(): Promise<Classroom[]> {
    return apiRequest<Classroom[]>('/classrooms');
  },

  async createClassroom(classroom: Omit<Classroom, 'id'>): Promise<Classroom> {
    return apiRequest<Classroom>('/classrooms', {
      method: 'POST',
      body: JSON.stringify(classroom),
    });
  },

  async updateClassroom(id: string, classroom: Partial<Classroom>): Promise<Classroom> {
    return apiRequest<Classroom>(`/classrooms/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(classroom),
    });
  },

  async deleteClassroom(id: string): Promise<void> {
    return apiRequest<void>(`/classrooms/${id}`, {
      method: 'DELETE',
    });
  },

  // Levels
  async getLevels(): Promise<Level[]> {
    return apiRequest<Level[]>('/levels');
  },

  async createLevel(level: Omit<Level, 'id'>): Promise<Level> {
    return apiRequest<Level>('/levels', {
      method: 'POST',
      body: JSON.stringify(level),
    });
  },

  async updateLevel(id: string, level: Partial<Level>): Promise<Level> {
    return apiRequest<Level>(`/levels/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(level),
    });
  },

  async deleteLevel(id: string): Promise<void> {
    return apiRequest<void>(`/levels/${id}`, {
      method: 'DELETE',
    });
  },

  // Time Slots
  async getTimeSlots(): Promise<TimeSlot[]> {
    return apiRequest<TimeSlot[]>('/timeslots');
  },

  async createTimeSlot(timeSlot: Omit<TimeSlot, 'id'>): Promise<TimeSlot> {
    return apiRequest<TimeSlot>('/timeslots', {
      method: 'POST',
      body: JSON.stringify(timeSlot),
    });
  },

  async updateTimeSlot(id: string, timeSlot: Partial<TimeSlot>): Promise<TimeSlot> {
    return apiRequest<TimeSlot>(`/timeslots/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(timeSlot),
    });
  },

  async deleteTimeSlot(id: string): Promise<void> {
    return apiRequest<void>(`/timeslots/${id}`, {
      method: 'DELETE',
    });
  },

  // Bulk operations
  async getScheduleData(): Promise<{
    teachers: Teacher[];
    classrooms: Classroom[];
    levels: Level[];
    timeSlots: TimeSlot[];
  }> {
    return apiRequest('/schedule/data');
  },

  async saveSchedule(data: {
    teachers: Teacher[];
    classrooms: Classroom[];
    levels: Level[];
    timeSlots: TimeSlot[];
  }): Promise<void> {
    return apiRequest('/schedule', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },
};
