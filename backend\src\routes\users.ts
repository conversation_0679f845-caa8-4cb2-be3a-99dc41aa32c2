import { Router } from 'express';
import Joi from 'joi';
import { UserController } from '@/controllers/userController';
import { authenticate } from '@/middleware/auth';
import { validate } from '@/utils/validation';

const router = Router();
const userController = new UserController();

// Validation schemas
const updateProfileSchema = Joi.object({
  firstName: Joi.string().trim().max(50).optional(),
  lastName: Joi.string().trim().max(50).optional(),
  avatar: Joi.string().uri().allow(null).optional(),
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(8).required(),
});

const deleteAccountSchema = Joi.object({
  password: Joi.string().required(),
});

// All user routes require authentication
router.use(authenticate);

// User profile routes
router.patch('/profile', validate(updateProfileSchema), userController.updateProfile);
router.patch('/password', validate(changePasswordSchema), userController.changePassword);
router.delete('/account', validate(deleteAccountSchema), userController.deleteAccount);

export default router;
