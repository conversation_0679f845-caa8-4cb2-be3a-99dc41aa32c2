import { Request, Response, NextFunction } from 'express';
import { Level } from '@/models/Level';
import { TimeSlot } from '@/models/TimeSlot';
import { AppError } from '@/utils/AppError';

export class LevelController {
  // Create new level
  public async createLevel(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const levelData = req.body;
      const level = new Level(levelData);
      await level.save();

      res.status(201).json({
        success: true,
        message: 'Level created successfully',
        data: level.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all levels
  public async getLevels(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const levels = await Level.find().sort({ label: 1 });

      res.json({
        success: true,
        message: 'Levels retrieved successfully',
        data: levels,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get level by ID
  public async getLevelById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const level = await Level.findById(id);

      if (!level) {
        throw new AppError('Level not found', 404);
      }

      res.json({
        success: true,
        message: 'Level retrieved successfully',
        data: level.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Update level
  public async updateLevel(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const level = await Level.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!level) {
        throw new AppError('Level not found', 404);
      }

      res.json({
        success: true,
        message: 'Level updated successfully',
        data: level.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete level
  public async deleteLevel(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      // Check if level has any time slots
      const timeSlots = await TimeSlot.find({ levelId: id });
      if (timeSlots.length > 0) {
        throw new AppError('Cannot delete level with existing time slots', 400);
      }

      const level = await Level.findByIdAndDelete(id);

      if (!level) {
        throw new AppError('Level not found', 404);
      }

      res.json({
        success: true,
        message: 'Level deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
