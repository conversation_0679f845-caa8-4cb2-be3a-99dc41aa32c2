import { Request, Response, NextFunction } from 'express';
import { Classroom } from '@/models/Classroom';
import { TimeSlot } from '@/models/TimeSlot';
import { AppError } from '@/utils/AppError';

export class ClassroomController {
  // Create new classroom
  public async createClassroom(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const classroomData = req.body;
      const classroom = new Classroom(classroomData);
      await classroom.save();

      res.status(201).json({
        success: true,
        message: 'Classroom created successfully',
        data: classroom.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all classrooms
  public async getClassrooms(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const classrooms = await Classroom.find().sort({ name: 1 });

      res.json({
        success: true,
        message: 'Classrooms retrieved successfully',
        data: classrooms,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get classroom by ID
  public async getClassroomById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const classroom = await Classroom.findById(id);

      if (!classroom) {
        throw new AppError('Classroom not found', 404);
      }

      res.json({
        success: true,
        message: 'Classroom retrieved successfully',
        data: classroom.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Update classroom
  public async updateClassroom(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const classroom = await Classroom.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!classroom) {
        throw new AppError('Classroom not found', 404);
      }

      res.json({
        success: true,
        message: 'Classroom updated successfully',
        data: classroom.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete classroom
  public async deleteClassroom(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      // Check if classroom has any time slots
      const timeSlots = await TimeSlot.find({ classroomId: id });
      if (timeSlots.length > 0) {
        throw new AppError('Cannot delete classroom with existing time slots', 400);
      }

      const classroom = await Classroom.findByIdAndDelete(id);

      if (!classroom) {
        throw new AppError('Classroom not found', 404);
      }

      res.json({
        success: true,
        message: 'Classroom deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
