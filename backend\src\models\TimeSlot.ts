import mongoose, { Schema, Document } from 'mongoose';

export interface ITimeSlot extends Document {
  _id: mongoose.Types.ObjectId;
  teacherId: mongoose.Types.ObjectId;
  classroomId: mongoose.Types.ObjectId;
  levelId: mongoose.Types.ObjectId;
  start: string;
  end: string;
  dayOfWeek: number;
  createdAt: Date;
  updatedAt: Date;
}

const timeSlotSchema = new Schema<ITimeSlot>(
  {
    teacherId: {
      type: Schema.Types.ObjectId,
      ref: 'Teacher',
      required: [true, 'Teacher ID is required'],
    },
    classroomId: {
      type: Schema.Types.ObjectId,
      ref: 'Classroom',
      required: [true, 'Classroom ID is required'],
    },
    levelId: {
      type: Schema.Types.ObjectId,
      ref: 'Level',
      required: [true, 'Level ID is required'],
    },
    start: {
      type: String,
      required: [true, 'Start time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid start time format (HH:MM)'],
    },
    end: {
      type: String,
      required: [true, 'End time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid end time format (HH:MM)'],
    },
    dayOfWeek: {
      type: Number,
      required: [true, 'Day of week is required'],
      min: [0, 'Day of week must be between 0 and 6'],
      max: [6, 'Day of week must be between 0 and 6'],
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for better query performance
timeSlotSchema.index({ teacherId: 1, dayOfWeek: 1, start: 1 });
timeSlotSchema.index({ classroomId: 1, dayOfWeek: 1, start: 1 });
timeSlotSchema.index({ dayOfWeek: 1, start: 1 });

// Validation to ensure end time is after start time
timeSlotSchema.pre('save', function(next) {
  const startTime = this.start.split(':').map(Number);
  const endTime = this.end.split(':').map(Number);
  
  const startMinutes = startTime[0] * 60 + startTime[1];
  const endMinutes = endTime[0] * 60 + endTime[1];
  
  if (endMinutes <= startMinutes) {
    next(new Error('End time must be after start time'));
  } else {
    next();
  }
});

export const TimeSlot = mongoose.model<ITimeSlot>('TimeSlot', timeSlotSchema);
