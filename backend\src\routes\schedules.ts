import { Router } from 'express';
import { ScheduleController } from '@/controllers/scheduleController';
import { authenticate } from '@/middleware/auth';
import { validate, createScheduleSchema, updateScheduleSchema } from '@/utils/validation';

const router = Router();
const scheduleController = new ScheduleController();

// All schedule routes require authentication
router.use(authenticate);

// Schedule CRUD routes
router.post('/', validate(createScheduleSchema), scheduleController.createSchedule);
router.get('/', scheduleController.getSchedules);
router.get('/default', scheduleController.getDefaultSchedule);
router.get('/:id', scheduleController.getScheduleById);
router.patch('/:id', validate(updateScheduleSchema), scheduleController.updateSchedule);
router.delete('/:id', scheduleController.deleteSchedule);

export default router;
