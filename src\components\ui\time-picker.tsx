import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TimePickerProps {
  value?: string;
  onChange: (time: string) => void;
  placeholder?: string;
  className?: string;
}

export const TimePicker: React.FC<TimePickerProps> = ({
  value,
  onChange,
  placeholder = "Select time",
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedHour, setSelectedHour] = useState<string>('');
  const [selectedMinute, setSelectedMinute] = useState<string>('');
  const [selectedPeriod, setSelectedPeriod] = useState<string>('AM');

  // Parse existing value when component mounts or value changes
  React.useEffect(() => {
    if (value) {
      const [time] = value.split(' ');
      const [hour, minute] = time.split(':');
      const hourNum = parseInt(hour);
      
      if (hourNum === 0) {
        setSelectedHour('12');
        setSelectedPeriod('AM');
      } else if (hourNum < 12) {
        setSelectedHour(hourNum.toString());
        setSelectedPeriod('AM');
      } else if (hourNum === 12) {
        setSelectedHour('12');
        setSelectedPeriod('PM');
      } else {
        setSelectedHour((hourNum - 12).toString());
        setSelectedPeriod('PM');
      }
      
      setSelectedMinute(minute);
    }
  }, [value]);

  // Generate hours (1-12)
  const hours = Array.from({ length: 12 }, (_, i) => (i + 1).toString());
  
  // Generate minutes (00, 05, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55)
  const minutes = Array.from({ length: 12 }, (_, i) => (i * 5).toString().padStart(2, '0'));

  const handleTimeChange = (hour?: string, minute?: string, period?: string) => {
    const h = hour || selectedHour;
    const m = minute || selectedMinute;
    const p = period || selectedPeriod;

    if (h && m && p) {
      // Convert to 24-hour format
      let hour24 = parseInt(h);
      if (p === 'AM' && hour24 === 12) {
        hour24 = 0;
      } else if (p === 'PM' && hour24 !== 12) {
        hour24 += 12;
      }

      const timeString = `${hour24.toString().padStart(2, '0')}:${m}`;
      onChange(timeString);
      setIsOpen(false);
    }
  };

  const displayValue = value ? (() => {
    const [time] = value.split(' ');
    const [hour, minute] = time.split(':');
    const hourNum = parseInt(hour);
    
    if (hourNum === 0) {
      return `12:${minute} AM`;
    } else if (hourNum < 12) {
      return `${hourNum}:${minute} AM`;
    } else if (hourNum === 12) {
      return `12:${minute} PM`;
    } else {
      return `${hourNum - 12}:${minute} PM`;
    }
  })() : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !value && "text-muted-foreground",
            className
          )}
        >
          <Clock className="mr-2 h-4 w-4" />
          {displayValue}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-4" align="start">
        <div className="space-y-4">
          <div className="text-sm font-medium">Select Time</div>
          <div className="grid grid-cols-3 gap-2">
            <div className="space-y-2">
              <label className="text-xs font-medium">Hour</label>
              <Select
                value={selectedHour}
                onValueChange={(hour) => {
                  setSelectedHour(hour);
                  handleTimeChange(hour, undefined, undefined);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Hour" />
                </SelectTrigger>
                <SelectContent>
                  {hours.map((hour) => (
                    <SelectItem key={hour} value={hour}>
                      {hour}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-xs font-medium">Minute</label>
              <Select
                value={selectedMinute}
                onValueChange={(minute) => {
                  setSelectedMinute(minute);
                  handleTimeChange(undefined, minute, undefined);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Min" />
                </SelectTrigger>
                <SelectContent>
                  {minutes.map((minute) => (
                    <SelectItem key={minute} value={minute}>
                      {minute}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-xs font-medium">Period</label>
              <Select
                value={selectedPeriod}
                onValueChange={(period) => {
                  setSelectedPeriod(period);
                  handleTimeChange(undefined, undefined, period);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="AM/PM" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="AM">AM</SelectItem>
                  <SelectItem value="PM">PM</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
