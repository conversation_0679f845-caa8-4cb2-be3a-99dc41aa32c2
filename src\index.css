
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 240 20% 97%;
    --foreground: 240 19% 16%;

    --card: 0 0% 100%;
    --card-foreground: 240 19% 16%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 19% 16%;

    --primary: 240 80% 60%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 80% 60%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-indigo-50 via-white to-purple-50 text-foreground min-h-screen;
    background-attachment: fixed;
  }
}

@layer components {
  .glass-card {
    @apply backdrop-blur-md bg-glass-light border border-white/20 shadow-xl;
  }
  
  .glass-button {
    @apply backdrop-blur-md bg-glass-medium border border-white/30 hover:bg-glass-light transition-all duration-300 shadow-lg hover:shadow-xl;
  }
  
  .liquid-container {
    @apply relative overflow-hidden;
  }
  
  .liquid-background::before {
    content: '';
    @apply absolute inset-0 bg-liquid-gradient animate-gradient-shift;
    background-size: 200% 200%;
  }
  
  .shine-effect {
    @apply relative overflow-hidden;
  }
  
  .shine-effect::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    transform: translateX(-100%) skewX(-15deg);
  }
  
  .shine-effect:hover::after {
    animation: glass-shine 0.6s ease-out;
  }
}
