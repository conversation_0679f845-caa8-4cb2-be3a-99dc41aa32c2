import { Router } from 'express';
import { TeacherController } from '@/controllers/teacherController';
import { validate, createTeacherSchema, updateTeacherSchema } from '@/utils/validation';

const router = Router();
const teacherController = new TeacherController();

// Teacher CRUD routes
router.post('/', validate(createTeacherSchema), teacherController.createTeacher);
router.get('/', teacherController.getTeachers);
router.get('/:id', teacherController.getTeacherById);
router.patch('/:id', validate(updateTeacherSchema), teacherController.updateTeacher);
router.delete('/:id', teacherController.deleteTeacher);

export default router;
