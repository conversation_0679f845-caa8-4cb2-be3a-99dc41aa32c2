
import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Sidebar } from '@/components/Sidebar';
import { TimetableGrid } from '@/components/TimetableGrid';
import { useStore } from '@/store/useStore';

const Index = () => {
  const { loadScheduleData, isLoading } = useStore();

  useEffect(() => {
    // Load initial data when component mounts
    loadScheduleData();
  }, [loadScheduleData]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="flex h-screen">
        <motion.div
          initial={{ x: -300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Sidebar />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="flex-1 p-6 overflow-auto"
        >
          <TimetableGrid />
        </motion.div>
      </div>
    </div>
  );
};

export default Index;
